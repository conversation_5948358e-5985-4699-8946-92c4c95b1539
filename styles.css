/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    text-align: center;
    margin-bottom: 30px;
}

h1 {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.game-info {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.current-player {
    font-weight: bold;
    font-size: 1.1rem;
}

.player-black {
    color: #000;
}

.player-white {
    color: #666;
}

.game-status {
    font-size: 1rem;
    color: #555;
}

.ai-thinking {
    color: #ff6b35;
    font-weight: bold;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.reset-btn {
    background: #ff6b35;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.reset-btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* Game board styles */
.game-board-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.game-board {
    background: #deb887;
    border: 3px solid #8b4513;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(15, 1fr);
    gap: 1px;
    width: 600px;
    height: 600px;
}

.cell {
    background: #deb887;
    border: 1px solid #8b4513;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: background-color 0.2s ease;
}

.cell:hover:not(.occupied) {
    background: #d4af7a;
}

.cell.occupied {
    cursor: not-allowed;
}

.stone {
    width: 85%;
    height: 85%;
    border-radius: 50%;
    box-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.stone.black {
    background: radial-gradient(circle at 30% 30%, #555, #000);
    border: 1px solid #333;
}

.stone.white {
    background: radial-gradient(circle at 30% 30%, #fff, #ddd);
    border: 1px solid #ccc;
}

.stone.winning {
    animation: glow 1s infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px #ff6b35; }
    to { box-shadow: 0 0 20px #ff6b35, 0 0 30px #ff6b35; }
}

/* Footer styles */
footer {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.game-rules h3 {
    margin-bottom: 10px;
    color: #333;
}

.game-rules ul {
    list-style-position: inside;
    line-height: 1.6;
}

.difficulty-info {
    text-align: right;
}

.difficulty-info p {
    margin-bottom: 5px;
    line-height: 1.6;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .game-board {
        width: 90vw;
        height: 90vw;
        max-width: 500px;
        max-height: 500px;
        padding: 10px;
    }
    
    .game-info {
        flex-direction: column;
        text-align: center;
    }
    
    footer {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .difficulty-info {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .game-board {
        width: 95vw;
        height: 95vw;
        padding: 5px;
    }
    
    .stone {
        width: 80%;
        height: 80%;
    }
}
