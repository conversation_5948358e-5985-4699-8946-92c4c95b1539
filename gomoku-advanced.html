<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gomoku - Advanced AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .game-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .current-player {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .player-black { color: #000; }
        .player-white { color: #666; }

        .game-status {
            font-size: 1rem;
            color: #555;
        }

        .ai-thinking {
            color: #ff6b35;
            font-weight: bold;
            display: none;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .reset-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .reset-btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .game-board {
            background: #deb887;
            border: 3px solid #8b4513;
            border-radius: 10px;
            padding: 10px;
            margin: 0 auto 20px;
            width: 600px;
            height: 600px;
            display: grid;
            grid-template-columns: repeat(15, 1fr);
            grid-template-rows: repeat(15, 1fr);
            gap: 2px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .cell {
            background: #deb887;
            border: 1px solid #8b4513;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            height: 100%;
            transition: background-color 0.2s ease;
        }

        .cell:hover:not(.occupied) {
            background: #d4af7a;
        }

        .cell.occupied {
            cursor: not-allowed;
        }

        .stone {
            width: 85%;
            height: 85%;
            border-radius: 50%;
            box-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .stone.black {
            background: radial-gradient(circle at 30% 30%, #555, #000);
            border: 1px solid #333;
        }

        .stone.white {
            background: radial-gradient(circle at 30% 30%, #fff, #ddd);
            border: 1px solid #ccc;
        }

        .stone.winning {
            animation: glow 1s infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px #ff6b35; }
            to { box-shadow: 0 0 20px #ff6b35, 0 0 30px #ff6b35; }
        }

        .game-rules {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            text-align: left;
            margin-bottom: 20px;
        }

        .game-rules h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .game-rules ul {
            list-style-position: inside;
            line-height: 1.6;
        }

        .difficulty-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .difficulty-info p {
            margin-bottom: 5px;
            line-height: 1.6;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .game-board {
                width: 90vw;
                height: 90vw;
                max-width: 500px;
                max-height: 500px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .game-info {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gomoku - Advanced AI</h1>
        <div class="game-info">
            <div class="current-player">
                <span id="current-player-text">Current Player: </span>
                <span id="current-player-indicator" class="player-black">Black (You)</span>
            </div>
            <div class="game-status">
                <span id="game-status">Click on the board to place your stone</span>
            </div>
            <div class="ai-thinking" id="ai-thinking">
                <span>🤔 AI is thinking...</span>
            </div>
        </div>
        <button id="reset-button" class="reset-btn">New Game</button>

        <div id="game-board" class="game-board">
            <!-- Board will be generated by JavaScript -->
        </div>

        <div class="difficulty-info">
            <p><strong>AI Difficulty:</strong> Advanced (Minimax with Alpha-Beta Pruning)</p>
            <p><strong>Search Depth:</strong> 4 levels</p>
            <p><strong>Features:</strong> Pattern Recognition, Threat Detection, Strategic Positioning</p>
        </div>

        <div class="game-rules">
            <h3>How to Play:</h3>
            <ul>
                <li>You play as Black stones, AI plays as White stones</li>
                <li>Click on any empty intersection to place your stone</li>
                <li>Get 5 stones in a row (horizontal, vertical, or diagonal) to win</li>
                <li>Block your opponent from getting 5 in a row</li>
                <li>The AI uses advanced algorithms to provide a challenging opponent</li>
            </ul>
        </div>
    </div>

    <script>
        class AdvancedGomokuGame {
            constructor() {
                this.BOARD_SIZE = 15;
                this.EMPTY = 0;
                this.BLACK = 1; // Human player
                this.WHITE = 2; // AI player
                this.WIN_LENGTH = 5;
                this.MAX_DEPTH = 4;
                
                this.board = [];
                this.currentPlayer = this.BLACK;
                this.gameOver = false;
                this.winner = null;
                this.winningStones = [];
                
                this.initializeBoard();
                this.createBoardUI();
                this.bindEvents();
                this.updateUI();
            }
            
            initializeBoard() {
                this.board = Array(this.BOARD_SIZE).fill().map(() => Array(this.BOARD_SIZE).fill(this.EMPTY));
                this.gameOver = false;
                this.winner = null;
                this.winningStones = [];
                this.currentPlayer = this.BLACK;
            }
            
            createBoardUI() {
                const boardElement = document.getElementById('game-board');
                boardElement.innerHTML = '';
                
                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.dataset.row = row;
                        cell.dataset.col = col;
                        boardElement.appendChild(cell);
                    }
                }
            }
            
            bindEvents() {
                const boardElement = document.getElementById('game-board');
                const resetButton = document.getElementById('reset-button');
                
                boardElement.addEventListener('click', (e) => {
                    if (e.target.classList.contains('cell') && !this.gameOver && this.currentPlayer === this.BLACK) {
                        const row = parseInt(e.target.dataset.row);
                        const col = parseInt(e.target.dataset.col);
                        this.makeMove(row, col);
                    }
                });
                
                resetButton.addEventListener('click', () => {
                    this.resetGame();
                });
            }
            
            makeMove(row, col) {
                if (this.board[row][col] !== this.EMPTY || this.gameOver) {
                    return false;
                }
                
                this.board[row][col] = this.currentPlayer;
                this.updateCellUI(row, col);
                
                if (this.checkWin(row, col)) {
                    this.endGame(this.currentPlayer);
                    return true;
                }
                
                if (this.isBoardFull()) {
                    this.endGame(null);
                    return true;
                }
                
                this.currentPlayer = this.currentPlayer === this.BLACK ? this.WHITE : this.BLACK;
                this.updateUI();
                
                if (this.currentPlayer === this.WHITE && !this.gameOver) {
                    this.makeAIMove();
                }
                
                return true;
            }
            
            updateCellUI(row, col) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.classList.add('occupied');

                const stone = document.createElement('div');
                stone.className = `stone ${this.currentPlayer === this.BLACK ? 'black' : 'white'}`;
                cell.appendChild(stone);
            }

            checkWin(row, col) {
                const directions = [
                    [0, 1],   // horizontal
                    [1, 0],   // vertical
                    [1, 1],   // diagonal \
                    [1, -1]   // diagonal /
                ];

                const player = this.board[row][col];

                for (const [dx, dy] of directions) {
                    let count = 1;
                    const stones = [[row, col]];

                    // Check positive direction
                    for (let i = 1; i < this.WIN_LENGTH; i++) {
                        const newRow = row + dx * i;
                        const newCol = col + dy * i;
                        if (this.isValidPosition(newRow, newCol) && this.board[newRow][newCol] === player) {
                            count++;
                            stones.push([newRow, newCol]);
                        } else {
                            break;
                        }
                    }

                    // Check negative direction
                    for (let i = 1; i < this.WIN_LENGTH; i++) {
                        const newRow = row - dx * i;
                        const newCol = col - dy * i;
                        if (this.isValidPosition(newRow, newCol) && this.board[newRow][newCol] === player) {
                            count++;
                            stones.unshift([newRow, newCol]);
                        } else {
                            break;
                        }
                    }

                    if (count >= this.WIN_LENGTH) {
                        this.winningStones = stones.slice(0, this.WIN_LENGTH);
                        return true;
                    }
                }

                return false;
            }

            isValidPosition(row, col) {
                return row >= 0 && row < this.BOARD_SIZE && col >= 0 && col < this.BOARD_SIZE;
            }

            isBoardFull() {
                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        if (this.board[row][col] === this.EMPTY) {
                            return false;
                        }
                    }
                }
                return true;
            }

            endGame(winner) {
                this.gameOver = true;
                this.winner = winner;

                if (this.winningStones.length > 0) {
                    this.highlightWinningStones();
                }

                this.updateUI();
            }

            highlightWinningStones() {
                this.winningStones.forEach(([row, col]) => {
                    const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    const stone = cell.querySelector('.stone');
                    if (stone) {
                        stone.classList.add('winning');
                    }
                });
            }

            updateUI() {
                const currentPlayerText = document.getElementById('current-player-indicator');
                const gameStatus = document.getElementById('game-status');
                const aiThinking = document.getElementById('ai-thinking');

                if (this.gameOver) {
                    if (this.winner === this.BLACK) {
                        gameStatus.textContent = '🎉 You won! Congratulations!';
                        currentPlayerText.textContent = 'Game Over';
                    } else if (this.winner === this.WHITE) {
                        gameStatus.textContent = '🤖 AI won! Better luck next time!';
                        currentPlayerText.textContent = 'Game Over';
                    } else {
                        gameStatus.textContent = '🤝 It\'s a draw!';
                        currentPlayerText.textContent = 'Game Over';
                    }
                    aiThinking.style.display = 'none';
                } else {
                    if (this.currentPlayer === this.BLACK) {
                        currentPlayerText.textContent = 'Black (You)';
                        currentPlayerText.className = 'player-black';
                        gameStatus.textContent = 'Your turn - Click to place your stone';
                        aiThinking.style.display = 'none';
                    } else {
                        currentPlayerText.textContent = 'White (AI)';
                        currentPlayerText.className = 'player-white';
                        gameStatus.textContent = 'AI is thinking...';
                        aiThinking.style.display = 'block';
                    }
                }
            }

            resetGame() {
                this.initializeBoard();
                this.createBoardUI();
                this.updateUI();
            }

            // Advanced AI Implementation with Minimax and Alpha-Beta Pruning
            async makeAIMove() {
                await new Promise(resolve => setTimeout(resolve, 800));

                const bestMove = this.findBestMove();
                if (bestMove) {
                    this.makeMove(bestMove.row, bestMove.col);
                }
            }

            findBestMove() {
                const moves = this.generateMoves();
                if (moves.length === 0) return null;

                // If it's the first move, play in the center
                if (moves.length === this.BOARD_SIZE * this.BOARD_SIZE) {
                    const center = Math.floor(this.BOARD_SIZE / 2);
                    return { row: center, col: center };
                }

                let bestMove = null;
                let bestScore = -Infinity;

                // Order moves by evaluation for better alpha-beta pruning
                const orderedMoves = this.orderMoves(moves);

                for (const move of orderedMoves.slice(0, 20)) { // Limit to top 20 moves for performance
                    this.board[move.row][move.col] = this.WHITE;

                    const score = this.minimax(this.MAX_DEPTH - 1, false, -Infinity, Infinity);

                    this.board[move.row][move.col] = this.EMPTY;

                    if (score > bestScore) {
                        bestScore = score;
                        bestMove = move;
                    }
                }

                return bestMove;
            }

            minimax(depth, isMaximizing, alpha, beta) {
                // Check for terminal states
                const evaluation = this.evaluateBoard();

                // If game is won/lost or max depth reached
                if (depth === 0 || Math.abs(evaluation) >= 10000) {
                    return evaluation;
                }

                const moves = this.generateMoves();
                if (moves.length === 0) return 0; // Draw

                if (isMaximizing) {
                    let maxEval = -Infinity;

                    for (const move of this.orderMoves(moves).slice(0, 15)) {
                        this.board[move.row][move.col] = this.WHITE;
                        const eval = this.minimax(depth - 1, false, alpha, beta);
                        this.board[move.row][move.col] = this.EMPTY;

                        maxEval = Math.max(maxEval, eval);
                        alpha = Math.max(alpha, eval);

                        if (beta <= alpha) break; // Alpha-beta pruning
                    }

                    return maxEval;
                } else {
                    let minEval = Infinity;

                    for (const move of this.orderMoves(moves).slice(0, 15)) {
                        this.board[move.row][move.col] = this.BLACK;
                        const eval = this.minimax(depth - 1, true, alpha, beta);
                        this.board[move.row][move.col] = this.EMPTY;

                        minEval = Math.min(minEval, eval);
                        beta = Math.min(beta, eval);

                        if (beta <= alpha) break; // Alpha-beta pruning
                    }

                    return minEval;
                }
            }

            generateMoves() {
                const moves = [];
                const range = 2; // Only consider moves within 2 cells of existing stones

                if (this.isEmpty()) {
                    const center = Math.floor(this.BOARD_SIZE / 2);
                    return [{ row: center, col: center }];
                }

                const considered = new Set();

                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        if (this.board[row][col] !== this.EMPTY) {
                            for (let dr = -range; dr <= range; dr++) {
                                for (let dc = -range; dc <= range; dc++) {
                                    const newRow = row + dr;
                                    const newCol = col + dc;
                                    const key = `${newRow},${newCol}`;

                                    if (this.isValidPosition(newRow, newCol) &&
                                        this.board[newRow][newCol] === this.EMPTY &&
                                        !considered.has(key)) {
                                        moves.push({ row: newRow, col: newCol });
                                        considered.add(key);
                                    }
                                }
                            }
                        }
                    }
                }

                return moves;
            }

            isEmpty() {
                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        if (this.board[row][col] !== this.EMPTY) {
                            return false;
                        }
                    }
                }
                return true;
            }

            // Position evaluation function
            evaluateBoard() {
                let score = 0;

                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        if (this.board[row][col] !== this.EMPTY) {
                            score += this.evaluatePosition(row, col, this.board[row][col]);
                        }
                    }
                }

                return score;
            }

            evaluatePosition(row, col, player) {
                const directions = [
                    [0, 1],   // horizontal
                    [1, 0],   // vertical
                    [1, 1],   // diagonal \
                    [1, -1]   // diagonal /
                ];

                let totalScore = 0;

                for (const [dx, dy] of directions) {
                    const pattern = this.getPattern(row, col, dx, dy, player);
                    totalScore += this.scorePattern(pattern, player);
                }

                // Add center bias
                const centerDistance = Math.abs(row - Math.floor(this.BOARD_SIZE / 2)) +
                                      Math.abs(col - Math.floor(this.BOARD_SIZE / 2));
                const centerBonus = Math.max(0, 10 - centerDistance);

                return totalScore + (player === this.WHITE ? centerBonus : -centerBonus);
            }

            getPattern(row, col, dx, dy, player) {
                let pattern = '';
                const range = 4;

                for (let i = -range; i <= range; i++) {
                    const newRow = row + dx * i;
                    const newCol = col + dy * i;

                    if (!this.isValidPosition(newRow, newCol)) {
                        pattern += 'X'; // Wall
                    } else if (this.board[newRow][newCol] === player) {
                        pattern += 'O'; // Own stone
                    } else if (this.board[newRow][newCol] === this.EMPTY) {
                        pattern += '.'; // Empty
                    } else {
                        pattern += 'X'; // Opponent stone
                    }
                }

                return pattern;
            }

            scorePattern(pattern, player) {
                const multiplier = player === this.WHITE ? 1 : -1;
                let score = 0;

                // Winning patterns (5 in a row)
                if (pattern.includes('OOOOO')) {
                    return 10000 * multiplier;
                }

                // Four in a row patterns
                if (pattern.includes('.OOOO.')) score += 5000;
                else if (pattern.includes('.OOOO') || pattern.includes('OOOO.')) score += 1000;

                // Three in a row patterns
                if (pattern.includes('.OOO.')) score += 500;
                else if (pattern.includes('.OOO') || pattern.includes('OOO.')) score += 100;

                // Two in a row patterns
                if (pattern.includes('.OO.')) score += 50;
                else if (pattern.includes('.OO') || pattern.includes('OO.')) score += 10;

                // Single stone patterns
                if (pattern.includes('.O.')) score += 5;

                return score * multiplier;
            }

            // Move ordering for better alpha-beta pruning
            orderMoves(moves) {
                return moves.map(move => ({
                    ...move,
                    score: this.evaluateMoveQuick(move.row, move.col)
                })).sort((a, b) => b.score - a.score);
            }

            evaluateMoveQuick(row, col) {
                let score = 0;

                // Check immediate threats and opportunities
                this.board[row][col] = this.WHITE;
                if (this.checkWin(row, col)) score += 10000;
                this.board[row][col] = this.EMPTY;

                this.board[row][col] = this.BLACK;
                if (this.checkWin(row, col)) score += 5000; // Block opponent win
                this.board[row][col] = this.EMPTY;

                // Center preference
                const centerDistance = Math.abs(row - Math.floor(this.BOARD_SIZE / 2)) +
                                      Math.abs(col - Math.floor(this.BOARD_SIZE / 2));
                score += Math.max(0, 20 - centerDistance);

                return score;
            }
        }

        // Initialize the game when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new AdvancedGomokuGame();
        });
    </script>
</body>
</html>
