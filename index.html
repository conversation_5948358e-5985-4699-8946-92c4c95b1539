<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gomoku - Five in a Row</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .game-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .current-player {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .player-black { color: #000; }
        .player-white { color: #666; }

        .game-status {
            font-size: 1rem;
            color: #555;
        }

        .ai-thinking {
            color: #ff6b35;
            font-weight: bold;
            display: none;
        }

        .reset-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .reset-btn:hover {
            background: #e55a2b;
        }

        .game-board {
            background: #deb887;
            border: 3px solid #8b4513;
            border-radius: 10px;
            padding: 10px;
            margin: 0 auto 20px;
            width: 600px;
            height: 600px;
            display: grid;
            grid-template-columns: repeat(15, 1fr);
            grid-template-rows: repeat(15, 1fr);
            gap: 2px;
        }

        .cell {
            background: #deb887;
            border: 1px solid #8b4513;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            height: 100%;
        }

        .cell:hover:not(.occupied) {
            background: #d4af7a;
        }

        .cell.occupied {
            cursor: not-allowed;
        }

        .stone {
            width: 85%;
            height: 85%;
            border-radius: 50%;
            box-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stone.black {
            background: radial-gradient(circle at 30% 30%, #555, #000);
            border: 1px solid #333;
        }

        .stone.white {
            background: radial-gradient(circle at 30% 30%, #fff, #ddd);
            border: 1px solid #ccc;
        }

        .stone.winning {
            box-shadow: 0 0 15px #ff6b35;
        }

        .game-rules {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            text-align: left;
        }

        .game-rules h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .game-rules ul {
            list-style-position: inside;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .game-board {
                width: 90vw;
                height: 90vw;
                max-width: 500px;
                max-height: 500px;
            }

            h1 {
                font-size: 2rem;
            }

            .game-info {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gomoku - Five in a Row</h1>
        <div class="game-info">
            <div class="current-player">
                <span id="current-player-text">Current Player: </span>
                <span id="current-player-indicator" class="player-black">Black (You)</span>
            </div>
            <div class="game-status">
                <span id="game-status">Click on the board to place your stone</span>
            </div>
            <div class="ai-thinking" id="ai-thinking">
                <span>🤔 AI is thinking...</span>
            </div>
        </div>
        <button id="reset-button" class="reset-btn">New Game</button>

        <div id="game-board" class="game-board">
            <!-- Board will be generated by JavaScript -->
        </div>

        <div class="game-rules">
            <h3>How to Play:</h3>
            <ul>
                <li>You play as Black stones, AI plays as White stones</li>
                <li>Click on any empty intersection to place your stone</li>
                <li>Get 5 stones in a row (horizontal, vertical, or diagonal) to win</li>
                <li>Block your opponent from getting 5 in a row</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('Script loading...');

        class GomokuGame {
            constructor() {
                console.log('Game initializing...');
                this.BOARD_SIZE = 15;
                this.EMPTY = 0;
                this.BLACK = 1; // Human player
                this.WHITE = 2; // AI player
                this.WIN_LENGTH = 5;
                this.MAX_DEPTH = 4;

                this.board = [];
                this.currentPlayer = this.BLACK;
                this.gameOver = false;
                this.winner = null;
                this.winningStones = [];

                this.initializeBoard();
                this.createBoardUI();
                this.bindEvents();
                this.updateUI();
                console.log('Game initialized successfully!');
            }

            initializeBoard() {
                this.board = Array(this.BOARD_SIZE).fill().map(() => Array(this.BOARD_SIZE).fill(this.EMPTY));
                this.gameOver = false;
                this.winner = null;
                this.winningStones = [];
                this.currentPlayer = this.BLACK;
            }

            createBoardUI() {
                console.log('Creating board UI...');
                const boardElement = document.getElementById('game-board');
                if (!boardElement) {
                    console.error('Board element not found!');
                    return;
                }

                boardElement.innerHTML = '';

                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.dataset.row = row;
                        cell.dataset.col = col;
                        boardElement.appendChild(cell);
                    }
                }
                console.log('Board UI created with', this.BOARD_SIZE * this.BOARD_SIZE, 'cells');
            }

            bindEvents() {
                console.log('Binding events...');
                const boardElement = document.getElementById('game-board');
                const resetButton = document.getElementById('reset-button');

                if (boardElement) {
                    boardElement.addEventListener('click', (e) => {
                        console.log('Board clicked:', e.target);
                        if (e.target.classList.contains('cell') && !this.gameOver && this.currentPlayer === this.BLACK) {
                            const row = parseInt(e.target.dataset.row);
                            const col = parseInt(e.target.dataset.col);
                            console.log('Cell clicked:', row, col);
                            this.makeMove(row, col);
                        }
                    });
                }

                if (resetButton) {
                    resetButton.addEventListener('click', () => {
                        console.log('Reset button clicked');
                        this.resetGame();
                    });
                }
            }

            makeMove(row, col) {
                console.log('Making move:', row, col, 'Player:', this.currentPlayer);

                if (this.board[row][col] !== this.EMPTY || this.gameOver) {
                    console.log('Invalid move');
                    return false;
                }

                this.board[row][col] = this.currentPlayer;
                this.updateCellUI(row, col);

                if (this.checkWin(row, col)) {
                    this.endGame(this.currentPlayer);
                    return true;
                }

                if (this.isBoardFull()) {
                    this.endGame(null); // Draw
                    return true;
                }

                this.currentPlayer = this.currentPlayer === this.BLACK ? this.WHITE : this.BLACK;
                this.updateUI();

                // If it's AI's turn, make AI move
                if (this.currentPlayer === this.WHITE && !this.gameOver) {
                    this.makeAIMove();
                }

                return true;
            }

            updateCellUI(row, col) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                if (!cell) {
                    console.error('Cell not found:', row, col);
                    return;
                }

                cell.classList.add('occupied');

                const stone = document.createElement('div');
                stone.className = `stone ${this.currentPlayer === this.BLACK ? 'black' : 'white'}`;
                cell.appendChild(stone);

                console.log('Cell updated:', row, col, this.currentPlayer === this.BLACK ? 'black' : 'white');
            }

            checkWin(row, col) {
                const directions = [
                    [0, 1],   // horizontal
                    [1, 0],   // vertical
                    [1, 1],   // diagonal \
                    [1, -1]   // diagonal /
                ];

                const player = this.board[row][col];

                for (const [dx, dy] of directions) {
                    let count = 1;
                    const stones = [[row, col]];

                    // Check positive direction
                    for (let i = 1; i < this.WIN_LENGTH; i++) {
                        const newRow = row + dx * i;
                        const newCol = col + dy * i;
                        if (this.isValidPosition(newRow, newCol) && this.board[newRow][newCol] === player) {
                            count++;
                            stones.push([newRow, newCol]);
                        } else {
                            break;
                        }
                    }

                    // Check negative direction
                    for (let i = 1; i < this.WIN_LENGTH; i++) {
                        const newRow = row - dx * i;
                        const newCol = col - dy * i;
                        if (this.isValidPosition(newRow, newCol) && this.board[newRow][newCol] === player) {
                            count++;
                            stones.unshift([newRow, newCol]);
                        } else {
                            break;
                        }
                    }

                    if (count >= this.WIN_LENGTH) {
                        this.winningStones = stones.slice(0, this.WIN_LENGTH);
                        return true;
                    }
                }

                return false;
            }

            isValidPosition(row, col) {
                return row >= 0 && row < this.BOARD_SIZE && col >= 0 && col < this.BOARD_SIZE;
            }

            isBoardFull() {
                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        if (this.board[row][col] === this.EMPTY) {
                            return false;
                        }
                    }
                }
                return true;
            }

            endGame(winner) {
                this.gameOver = true;
                this.winner = winner;

                if (this.winningStones.length > 0) {
                    this.highlightWinningStones();
                }

                this.updateUI();
            }

            highlightWinningStones() {
                this.winningStones.forEach(([row, col]) => {
                    const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    const stone = cell.querySelector('.stone');
                    if (stone) {
                        stone.classList.add('winning');
                    }
                });
            }

            updateUI() {
                const currentPlayerText = document.getElementById('current-player-indicator');
                const gameStatus = document.getElementById('game-status');
                const aiThinking = document.getElementById('ai-thinking');

                if (this.gameOver) {
                    if (this.winner === this.BLACK) {
                        gameStatus.textContent = '🎉 You won! Congratulations!';
                        currentPlayerText.textContent = 'Game Over';
                    } else if (this.winner === this.WHITE) {
                        gameStatus.textContent = '🤖 AI won! Better luck next time!';
                        currentPlayerText.textContent = 'Game Over';
                    } else {
                        gameStatus.textContent = '🤝 It\'s a draw!';
                        currentPlayerText.textContent = 'Game Over';
                    }
                    aiThinking.style.display = 'none';
                } else {
                    if (this.currentPlayer === this.BLACK) {
                        currentPlayerText.textContent = 'Black (You)';
                        currentPlayerText.className = 'player-black';
                        gameStatus.textContent = 'Your turn - Click to place your stone';
                        aiThinking.style.display = 'none';
                    } else {
                        currentPlayerText.textContent = 'White (AI)';
                        currentPlayerText.className = 'player-white';
                        gameStatus.textContent = 'AI is thinking...';
                        aiThinking.style.display = 'block';
                    }
                }
            }

            resetGame() {
                this.initializeBoard();
                this.createBoardUI();
                this.updateUI();
            }

            // Simple AI implementation
            async makeAIMove() {
                await new Promise(resolve => setTimeout(resolve, 1000));

                const moves = this.getAvailableMoves();
                if (moves.length === 0) return;

                // Simple AI: random move for now
                const randomMove = moves[Math.floor(Math.random() * moves.length)];
                this.makeMove(randomMove.row, randomMove.col);
            }

            getAvailableMoves() {
                const moves = [];
                for (let row = 0; row < this.BOARD_SIZE; row++) {
                    for (let col = 0; col < this.BOARD_SIZE; col++) {
                        if (this.board[row][col] === this.EMPTY) {
                            moves.push({ row, col });
                        }
                    }
                }
                return moves;
            }
        }

        // Initialize the game when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, initializing game...');
            window.game = new GomokuGame();
        });
    </script>
</body>
</html>
